<template>
  <div class="header">
    <!-- 左侧区域 -->
    <div class="header-left">
      <button v-if="showBackBtn" class="back-btn" style="margin-right: 5px" @click="$emit('back')">
        <img src="@/assets/icon/back.png" alt="返回" />
      </button>
      <div v-if="showHomeBtn" class="home-btn-container" @click.stop="$emit('home')">
        <div class="home-btn">
          <img
            :src="dynamicHomeAvatar"
            alt="AI助理"
          />
        </div>
        <!-- 当显示功能介绍时，隐藏头像下方的标签 -->
        <div v-if="!showFeatureIntro" class="home-label">{{ dynamicHomeLabel }}</div>
      </div>
      <!-- 功能介绍区域 -->
      <div v-if="showFeatureIntro" class="feature-intro-container">
        <div class="feature-intro-title">{{ dynamicFeatureTitle }}</div>
        <div class="feature-intro-description">{{ dynamicFeatureDescription }}</div>
      </div>
      <div
        v-if="showUserAvatar && !userLoading"
        class="user-avatar"
        :style="{ backgroundColor: getRandomColor(currentMisId) }"
        @click="$emit('avatar-click')"
      >
        {{ getAvatarLetter(currentMisId) }}
      </div>
      <div v-else-if="showUserAvatar && userLoading" class="user-avatar loading-avatar">
        <span class="loading-dot"></span>
      </div>
      <div
        v-if="showPersonListBtn"
        class="person-list-btn-container"
        @click.stop="$emit('person-list')"
      >
        <div class="person-list-btn">
          <img src="@/assets/icon/contactsPersonList.png" alt="人员列表" />
        </div>
        <div class="person-list-label">亲友名单</div>
      </div>

      <div v-if="showUserAvatar" class="placeholder"></div>
    </div>
    <!-- 中间区域 - Tab切换 -->
    <div v-if="showGraphTabSwitch" class="header-center">
      <div class="tab-switch" @click="$emit('toggle-graph-tab')">
        <div class="tab-slider" :class="{ 'slide-right': activeGraphTab === 'list' }"></div>
        <div class="tab-option" :class="{ active: activeGraphTab === 'graph' }">关系图</div>
        <div class="tab-option" :class="{ active: activeGraphTab === 'list' }">人员列表</div>
      </div>
    </div>

    <!-- 右侧按钮 -->
    <div class="header-right">
      <span v-if="showVoiceBtn" class="chat-voice" @click="$emit('voice')">
        <TrumpetIcon v-if="isChatPlay" width="40px" height="40px" color="var(--primary-color)" />
        <i v-else class="iconfont icon-roo-sqt-laba"></i>
      </span>

      <div v-if="showAddChatBtn" class="add-chat" @click.stop="$emit('add-chat')">
        <img v-if="addChatType === 'add'" src="@/assets/img/add-message.png" alt="新建" />
        <img v-else src="@/assets/img/search.svg" alt="搜索" />
      </div>
      <div
        v-if="showRelationshipBtn"
        class="relationship-btn-container"
        @click.stop="$emit('relationship')"
      >
        <div class="relationship-btn">
          <RelationshipIcon width="30px" height="30px" color="var(--primary-color)" />
        </div>
        <div class="relationship-label">关系图</div>
      </div>
      <div v-if="showWeatherBtn" class="weather-btn-container" @click.stop="$emit('weather')">
        <div class="weather-btn">
          <WeatherIcon width="25px" height="25px" color="var(--primary-color)" />
        </div>
        <div class="weather-label">天气</div>
      </div>
      <div v-if="showMemoBtn" class="memo-btn-container" @click.stop="$emit('memo')">
        <div class="memo-btn">
          <MemoIcon width="25px" height="25px" color="var(--primary-color)" />
        </div>
        <div class="memo-label">备忘录</div>
      </div>
      <div
        v-if="showHistoryBtn"
        class="history-btn-container"
        style="margin-right: 5px"
        @click.stop="$emit('history')"
      >
        <div class="history-btn">
          <HistoryIcon width="25px" height="25px" color="var(--primary-color)" />
        </div>
        <div class="history-label">历史会话</div>
      </div>
      <div v-if="showBackToIndexBtn" class="back-to-index-btn-container" @click.stop="$emit('back-to-index')">
        <div class="back-to-index-btn">
          <HomeIcon width="28px" height="28px" color="var(--primary-color)" />
        </div>
        <div class="back-to-index-label">回到首页</div>
      </div>
    </div>
    <div v-if="showHeaderGrad" class="header-grad"></div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';
import RelationshipIcon from '@/assets/icons/RelationshipIcon.vue';
import MemoIcon from '@/assets/icons/MemoIcon.vue';
import HistoryIcon from '@/assets/icons/HistoryIcon.vue';
import WeatherIcon from '@/assets/icons/WeatherIcon.vue';
import TrumpetIcon from '@/assets/icons/TrumpetIcon.vue';
import HomeIcon from '@/assets/icons/HomeIcon.vue';
import { useRoute } from 'vue-router';

// 导入功能图标
import questionIcon from '@/assets/assistant/question.png';
import relationIcon from '@/assets/assistant/relation.png';
import weatherIcon from '@/assets/assistant/weather.jpg';

const route = useRoute();

const props = defineProps({
  showBackBtn: Boolean,
  showHistoryBtn: Boolean,
  showUserAvatar: Boolean,
  showAssistantAvatar: Boolean,
  assistantAvatarSrc: {
    type: String,
    default: '@/assets/icon/assistant_avatar.png',
  },
  assistantName: {
    type: String,
    default: '备忘录',
  },
  selectedAssistantAvatar: {
    type: String,
    default: '@/assets/icon/assistant_avatar.png',
  },
  showVoiceBtn: Boolean,
  showPersonListBtn: Boolean,
  showRelationshipBtn: Boolean,
  showAddChatBtn: Boolean,
  showHomeBtn: Boolean,
  showWeatherBtn: Boolean,
  showMemoBtn: Boolean,
  showBackToIndexBtn: Boolean,
  showFeatureIntro: Boolean,
  addChatType: {
    type: String,
    default: 'add', // "add" | "search"
  },
  isChatPlay: Boolean,
  userLoading: Boolean,
  currentMisId: {
    type: String,
    default: '',
  },
  getRandomColor: {
    type: Function,
    default: () => '#ccc',
  },
  getAvatarLetter: {
    type: Function,
    default: () => '',
  },
  showHeaderGrad: Boolean,
  showGraphTabSwitch: Boolean,
  activeGraphTab: {
    type: String,
    default: 'graph',
  },
});

defineEmits([
  'back',
  'history',
  'voice',
  'person-list',
  'relationship',
  'add-chat',
  'home',
  'avatar-click',
  'memo',
  'weather',
  'toggle-graph-tab',
  'back-to-index',
]);

// 动态头像计算属性
const dynamicHomeAvatar = computed(() => {
  // 首先检查当前路由，如果是关系图页面，直接返回关系图图标
  if (route.name === 'relationship-graph') {
    console.log('🔍 [Maintopbar] 检测到关系图页面，返回关系图图标');
    return relationIcon;
  }

  // 检查路由查询参数中的cardType
  const cardType = route.query.cardType as string;

  // 检查sessionStorage中的chatFeature（用于从首页功能卡片进入的情况）
  const chatFeature = sessionStorage.getItem('chatFeature');

  console.log('🔍 [Maintopbar] 动态头像检测:', { cardType, chatFeature, routeName: route.name });

  // 根据功能来源返回对应图标
  if (cardType === 'question' || chatFeature === 'question') {
    return questionIcon;
  }
  if (cardType === 'relation' || chatFeature === 'relation') {
    return relationIcon;
  }
  if (cardType === 'weather' || chatFeature === 'weather') {
    return weatherIcon;
  }
  if (cardType === 'record' || chatFeature === 'laodong') {
    // 老董使用传入的助手头像
    return props.selectedAssistantAvatar || '@/assets/icon/assistant_avatar.png';
  }

  // 默认返回传入的助手头像
  return props.selectedAssistantAvatar || '@/assets/icon/assistant_avatar.png';
});

// 动态文案计算属性
const dynamicHomeLabel = computed(() => {
  // 首先检查当前路由，如果是关系图页面，直接返回"董亲友"
  if (route.name === 'relationship-graph') {
    console.log('🔍 [Maintopbar] 检测到关系图页面，返回"董亲友"');
    return '董亲友';
  }

  // 检查路由查询参数中的cardType
  const cardType = route.query.cardType as string;

  // 检查sessionStorage中的chatFeature（用于从首页功能卡片进入的情况）
  const chatFeature = sessionStorage.getItem('chatFeature');

  console.log('🔍 [Maintopbar] 动态文案检测:', { cardType, chatFeature, routeName: route.name });

  // 根据功能来源返回对应文案
  if (cardType === 'question' || chatFeature === 'question') {
    return '董问题';
  }
  if (cardType === 'relation' || chatFeature === 'relation') {
    return '董亲友'; // 修改为"董亲友"
  }
  if (cardType === 'weather' || chatFeature === 'weather') {
    return '董天气';
  }
  if (cardType === 'record' || chatFeature === 'laodong') {
    return '老董';
  }

  // 默认返回"老董"
  return '老董';
});

// 动态功能标题计算属性
const dynamicFeatureTitle = computed(() => {
  // 首先检查当前路由，如果是关系图页面，直接返回"董亲友"
  if (route.name === 'relationship-graph') {
    console.log('🔍 [Maintopbar] 检测到关系图页面，功能标题返回"董亲友"');
    return '董亲友';
  }

  // 检查路由查询参数中的cardType
  const cardType = route.query.cardType as string;

  // 检查sessionStorage中的chatFeature（用于从首页功能卡片进入的情况）
  const chatFeature = sessionStorage.getItem('chatFeature');

  // 根据功能来源返回对应标题
  if (cardType === 'question' || chatFeature === 'question') {
    return '董问题';
  }
  if (cardType === 'relation' || chatFeature === 'relation') {
    return '董亲友';
  }
  if (cardType === 'weather' || chatFeature === 'weather') {
    return '董天气';
  }
  if (cardType === 'record' || chatFeature === 'laodong') {
    return '老董';
  }

  // 默认返回"老董"
  return '老董';
});

// 动态功能描述计算属性
const dynamicFeatureDescription = computed(() => {
  // 检查路由查询参数中的cardType
  const cardType = route.query.cardType as string;

  // 检查sessionStorage中的chatFeature（用于从首页功能卡片进入的情况）
  const chatFeature = sessionStorage.getItem('chatFeature');

  // 根据功能来源返回对应描述
  if (cardType === 'question' || chatFeature === 'question') {
    return '随时问，随时答';
  }
  if (cardType === 'relation' || chatFeature === 'relation') {
    return '更懂你的人际关系专家';
  }
  if (cardType === 'weather' || chatFeature === 'weather') {
    return '更懂你的天气专家';
  }
  if (cardType === 'record' || chatFeature === 'laodong') {
    return '您的专属AI管家';
  }

  // 默认返回老董描述
  return '您的专属AI管家';
});
</script>

<style scoped lang="scss">
.header {
  height: 125px;
  padding: 10px 40px 40px;
  margin-bottom: 20px;
  box-sizing: border-box;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    .back-btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 72px; // 增大按钮尺寸
      height: 72px;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      img {
        width: 40px;
        height: 40px;
      }
    }

    .home-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .home-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--bg-glass);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;

        img {
          width: 70px;
          height: 70px;
          border-radius: 50%; // AI助理头像需要圆形显示
        }
      }

      .home-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .home-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .home-btn {
          transform: translateY(0);
        }
      }
    }

    .user-avatar {
      width: 72px; // 增大头像尺寸
      height: 72px;
      border-radius: 50%;
      color: var(--primary-color);
      font-size: 32px;
      font-weight: bold;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      background: var(--primary-color-light);
      border: 2px solid var(--primary-color);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-color-medium);
        transform: translateY(-2px);
      }

      &.loading-avatar {
        background: var(--bg-glass);
        border-color: var(--border-glass);

        .loading-dot {
          display: inline-block;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: var(--accent-color);
          animation: avatar-loading 1s infinite alternate;
        }
      }
    }

    .assistant-avatar-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;

      .assistant-label {
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }
    }


    .placeholder {
      width: 16px;
      height: 16px;
      visibility: hidden;
    }

    .feature-intro-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      // margin-left: px;
      flex: 1; // 允许功能介绍区域占用可用空间
      min-width: 0; // 允许收缩
      max-width: 600px; // 增加最大宽度，容纳更多文本

      .feature-intro-title {
        font-size: 40px;
        font-weight: 700;
        color: var(--primary-color);
        line-height: 1.2;
        margin-bottom: 4px;
        white-space: nowrap; // 防止标题换行
        overflow: hidden;
        text-overflow: ellipsis; // 超长时显示省略号
      }

      .feature-intro-description {
        font-size: 22px;
        font-weight: 400;
        color: var(--primary-color);
        line-height: 1.3;
        opacity: 0.8;
        white-space: nowrap; // 防止描述换行
        overflow: hidden;
        text-overflow: ellipsis; // 超长时显示省略号
      }
    }
  }

  // 通用的按钮容器样式，适用于左侧和右侧区域
  .person-list-btn-container {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    transition: all 0.3s ease;

    .person-list-btn {
      width: 72px; // 增大按钮尺寸
      height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      transition: all 0.3s ease;

      img {
        width: 40px;
        height: 40px;
      }
    }

    .person-list-label {
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      margin-top: 4px;
      font-size: 24px;
      color: var(--primary-color);
      font-weight: 500;
      white-space: nowrap;
      pointer-events: none; // 防止文字影响点击
    }

    &:hover {
      .person-list-btn {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }
    }

    &:active {
      .person-list-btn {
        transform: translateY(0);
      }
    }


  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 32px;

    .chat-voice {
      width: 96px !important; // 增大按钮尺寸，使用!important确保不被rem转换影响
      height: 96px !important;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      .iconfont {
        font-size: 40px;
        color: var(--primary-color);
      }
    }

    .add-chat {
      width: 72px; // 增大按钮尺寸
      height: 72px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--bg-glass);
      border: 2px solid var(--border-glass);
      backdrop-filter: blur(20px);
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: var(--bg-glass-hover);
        border-color: var(--border-accent);
        transform: translateY(-2px);
      }

      &:active {
        transform: translateY(0);
      }

      img {
        width: 40px;
        height: 40px;
      }
    }



    .relationship-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .relationship-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--primary-color-medium);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
      }

      .relationship-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .relationship-btn {
          background: var(--primary-color-strong);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .relationship-btn {
          transform: translateY(0);
        }
      }
    }

    .weather-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .weather-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--primary-color-medium);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
      }

      .weather-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .weather-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .weather-btn {
          transform: translateY(0);
        }
      }

      &:hover {
        .weather-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .weather-btn {
          transform: translateY(0);
        }
      }
    }

    .memo-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .memo-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--primary-color-medium);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
        
        img {
          width: 40px;
          height: 40px;
          filter: var(--icon-filter-primary); // 将图标变为主题色
        }
      }

      .memo-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .memo-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .memo-btn {
          transform: translateY(0);
        }
      }
    }



    .back-to-index-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .back-to-index-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--primary-color-medium);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
      }

      .back-to-index-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .back-to-index-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .back-to-index-btn {
          transform: translateY(0);
        }
      }
    }

    .history-btn-container {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
      transition: all 0.3s ease;

      .history-btn {
        width: 72px; // 增大按钮尺寸
        height: 72px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        background: var(--primary-color-medium);
        border: 2px solid var(--border-glass);
        backdrop-filter: blur(20px);
        transition: all 0.3s ease;
      }

      .history-label {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        margin-top: 4px;
        font-size: 24px;
        color: var(--primary-color);
        font-weight: 500;
        white-space: nowrap;
        pointer-events: none; // 防止文字影响点击
      }

      &:hover {
        .history-btn {
          background: var(--bg-glass-hover);
          border-color: var(--border-accent);
          transform: translateY(-2px);
        }
      }

      &:active {
        .history-btn {
          transform: translateY(0);
        }
      }
    }
  }

  .header-grad {
    position: absolute;
    top: 128px;
    left: 0px;
    width: 100%;
    height: 100px;
    opacity: 1;
    background: linear-gradient(180deg, var(--primary-color-light) 0%, rgba(0, 188, 212, 0) 100%);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .header {
    padding: 15px 20px;

    .header-left,
    .header-right {
      gap: 12px;

      .back-btn,
      .history-btn-container .history-btn,
      .relationship-btn,
      .user-avatar,
      .chat-voice,
      .person-list-btn-container .person-list-btn,
      .relationship-btn-container .relationship-btn,
      .add-chat,
      .memo-btn-container .memo-btn,
      .home-btn-container .home-btn,
      .back-to-index-btn-container .back-to-index-btn {
        width: 72px;
        height: 72px;
      }

      .assistant-avatar-container .avatar {
        width: 80px;
        height: 80px;

        img {
          width: 32px;
          height: 32px;
        }
      }

      .history-btn-container .history-label,
      .person-list-btn-container .person-list-label,
      .relationship-btn-container .relationship-label,
      .weather-btn-container .weather-label,
      .memo-btn-container .memo-label,
      .home-btn-container .home-label,
      .back-to-index-btn-container .back-to-index-label,
      .assistant-avatar-container .assistant-label {
        font-size: 20px;
        margin-top: 2px;
      }

      .user-avatar {
        font-size: 24px;
      }

      .feature-intro-container {
        margin-left: 12px;
        max-width: 300px; // 移动端增加最大宽度

        .feature-intro-title {
          font-size: 30px;
          margin-bottom: 2px;
        }

        .feature-intro-description {
          font-size: 22px;
          font-weight: 500;
        }
      }

      .chat-voice .iconfont {
        font-size: 32px;
      }
    }
  }
}
@keyframes avatar-loading {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0.5;
  }
}

// 中间区域样式
.header-center {
  display: flex;
  justify-content: center;
  flex: 1;
  margin-bottom: 0%;

  .tab-switch {
    position: relative;
    display: flex;
    background: var(--bg-glass);
    border: 2px solid var(--primary-color);
    border-radius: 32px;
    padding: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    width: 400px;
    min-width: 400px;
    height: 64px;
    backdrop-filter: blur(20px);
    box-shadow:
      0 0 15px var(--primary-color-light),
      var(--shadow-accent);

    &:hover {
      background: var(--bg-glass-hover);
      transform: translateY(-1px);
      box-shadow:
        0 0 20px var(--primary-color-medium),
        var(--shadow-accent);
    }

    .tab-slider {
      position: absolute;
      top: 8px;
      left: 8px;
      width: calc(50% - 8px);
      height: calc(100% - 16px);
      background: var(--primary-color);
      border-radius: 24px;
      transition: transform 0.3s ease;
      box-shadow:
        0 0 10px var(--primary-color-medium),
        inset 0 0 10px rgba(255, 255, 255, 0.1);

      &.slide-right {
        transform: translateX(100%);
      }
    }

    .tab-option {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 22px;
      font-weight: 600;
      color: var(--primary-color);
      transition: color 0.3s ease;
      z-index: 1;
      position: relative;
      padding: 16px 24px;

      &.active {
        color: var(--on-primary-text);
        font-weight: 700;
      }

      &:hover:not(.active) {
        color: var(--primary-color);
      }
    }
  }
}
</style>
